<%@ Page Title="" Language="C#" MasterPageFile="~/MasterPage.master" AutoEventWireup="true" CodeFile="index.aspx.cs" Inherits="index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="TitleContent" runat="Server">
    首页 - <%=uConfig.stcdata("sitename") %>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="" style="margin-bottom: 0px;">
        <div style="padding: 20px 10px;">

            <div style="margin-bottom: 20px; display: flex; overflow-x: auto; padding: 25px 0; gap: 15px;" class="classtype1">

                <a class="classtype_active" type="tk">
                    <img src="../static/images/b/tiktok.JPG" style="height: 32px;" />
                </a>
                <a type="ins">
                    <img src="../static/images/b/INS.JPG" style="height: 32px;" />
                </a>
                <a type="fb">
                    <img src="../static/images/b/FB.JPG" style="height: 32px;" />
                </a>
                <a type="ytb">
                    <img src="../static/images/b/youtube.JPG" style="height: 32px;" />
                </a>

                <a type="twitch">
                    <img src="../static/images/b/twitch.png" style="height: 32px;" />
                </a>

                <a type="shopee">
                    <img src="../static/images/b/shopee.png" style="height: 32px;" />
                </a>
                <a type="tw">
                        <img src="../static/images/b/twitter.JPG" style="height: 32px;" />
                    </a>
                <%--<a type="tg">
                        <img src="../static/images/b/telegram.PNG" style="height: 32px;" />
                    </a>--%>
                <a type="bigo">
                    <img src="../static/images/b/bigo.png" style="height: 32px;" />
                    </a>
                <a type="steam">
                    <img src="../static/images/b/steam.png" style="height: 32px;" />
                </a>

                <a type="lazada">
                    <img src="../static/images/b/lazada.png" style="height: 32px;" />
                </a>


            </div>

            <script id="order_business" type="text/ecmascript">
                <%=order_business %>
            </script>

            <style>
                .classtype1 {
                    scrollbar-width: thin;
                    scrollbar-color: #ddd transparent;
                }

                .classtype1::-webkit-scrollbar {
                    height: 6px;
                }

                .classtype1::-webkit-scrollbar-track {
                    background: transparent;
                }

                .classtype1::-webkit-scrollbar-thumb {
                    background: #ddd;
                    border-radius: 3px;
                }

                .classtype1 a {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 15px 18px;
                    background: #fff;
                    cursor: pointer;
                    border-radius: 12px;
                    border: 2px solid #f0f0f0;
                    transition: all 0.3s ease;
                    min-width: 60px;
                    flex-shrink: 0;
                }

                    .classtype1 a.classtype_active {
                        border-color: #3b82f6;
                        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
                        transform: translateY(-2px);
                    }

                    .classtype1 a.classtype_active img {
                        filter: brightness(0) invert(1);
                    }

                    .classtype1 a:hover {
                        transform: translateY(-3px);
                        border-color: #6366f1;
                        box-shadow: 0 10px 30px rgba(99, 102, 241, 0.25);
                    }

                .classtype2 a {
                    position: relative;
                    display: inline-block;
                    background: #f8fafc;
                    border: 2px solid #e2e8f0;
                    color: #64748b;
                    font-weight: 600;
                    padding: 12px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    text-decoration: none;
                    margin-right: 10px;
                    margin-bottom: 10px;
                    transition: all 0.3s ease;
                    font-size: 14px;
                }

                    .classtype2 a.classtype_active {
                        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                        border-color: #10b981;
                        color: #fff;
                        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
                        transform: translateY(-1px);
                    }

                    .classtype2 a:hover:not(.classtype_active) {
                        background: #e2e8f0;
                        border-color: #cbd5e1;
                        color: #475569;
                        transform: translateY(-1px);
                    }
            </style>

            <div style="margin-bottom: 25px; display: flex; flex-wrap: wrap; align-items: center;" class="classtype2">
                <a type="" class="classtype_active">全部</a>
<%--
                <a type="like">点赞</a>
                <a type="comment">评论</a>--%>
                <a type="online">人气</a>
                <a type="fans">粉丝</a>
                <a type="view">播放</a>

                <%--<a style="display:inline-block;background:#dc5454;color:#eee;font-weight:bold;padding:12px;border-radius:5px;cursor:pointer;text-decoration:none;">YouTube</a>--%>
            </div>

            <script>
                $('.classtype1 a').on('click', function () {
                    $(this).addClass('classtype_active').siblings().removeClass('classtype_active');

                    $(".classtype2 a").eq(0).addClass('classtype_active').siblings().removeClass('classtype_active');
                    refresh_classtype();
                })
                $('.classtype2 a').on('click', function () {
                    $(this).addClass('classtype_active').siblings().removeClass('classtype_active');


                    refresh_classtype();
                })

                function refresh_classtype() {
                    var obj = JSON.parse($('#order_business').html());
                    $('#orderTypes').html('<option value="">请选择业务</option>');

                    var type1 = $('.classtype1 a.classtype_active').attr("type");
                    var type2 = $('.classtype2 a.classtype_active').attr("type");

                    for (var i = 0; i < obj.length; i++) {
                        if (obj[i].typename == type1 && (type2 == "" || type2 == obj[i].type2)) {
                            var pretext = '★★★推荐★★★ | ';
                            pretext = "";
                            $('#orderTypes').append('<option value="' + obj[i].id + '/' + obj[i].param + '">' + pretext + obj[i].appname + '</option>');
                        }
                    }

                    $("#orderTypes").change();
                }



            </script>


            <div class="ccvoYb form-group">
                <label class="form-label">选择业务类型</label>
                <select id="orderTypes" class="w_input form-select">
                    <asp:Repeater ID="items" runat="server">
                        <ItemTemplate>
                            <option value="<%#Eval("id") %>/<%#Eval("param") %>">★★★推荐★★★ | <%#Eval("appname") %></option>
                        </ItemTemplate>
                    </asp:Repeater>
                </select>
            </div>

            <script>

                refresh_classtype();
            </script>

            <div class="ccvoYb form-group">
                <label class="form-label">链接信息</label>
                <textarea id="orderUrls" class="w_input form-textarea" placeholder="链接信息，批量下单格式（一行代表一个订单）：链接,数量" autocomplete="off" style="height: 200px;"></textarea>
            </div>

            <div class="ccvoYb hInput form-group" style="display: none;">
                <label class="form-label">订单备注</label>
                <input id="orderNote" class="w_input" placeholder="订单备注" autocomplete="off">
            </div>

            <div class="ccvoYb hInput form-group" style="display: none;">
                <label class="form-label">评论内容</label>
                <textarea id="orderComments" class="w_input form-textarea" placeholder="评论内容" autocomplete="off" style="height: 200px;"></textarea>
            </div>

            <div class="ccvoYb form-group">
                <label class="form-label">费用预览</label>
                <input id="orderTotal" class="w_input total-input" disabled="disabled" value="" autocomplete="off">
            </div>


            <div class="ccvoYb form-group">
                <label class="form-label">订单数量</label>
                <input id="orderNum" class="w_input" placeholder="订单数量" autocomplete="off">
            </div>

            <div class="ccvoYb hInput form-group" style="display: none;">
                <label class="form-label">直播时长</label>
                <input id="orderTime" class="w_input" placeholder="直播时长（小时）" autocomplete="off">
            </div>


            <div style="margin-top: 30px; display: flex; justify-content: center;">
                <a class="gb_9d submit-btn" style="width: 280px; text-align: center; cursor: pointer; display: block; font-size: 16px; padding: 15px 0; font-weight: 600;" id="orderTask_Btn">
                    <span class="btn-text">提交订单</span>
                    <span class="btn-loading" style="display: none;">处理中...</span>
                </a>
                <div style="clear: both;"></div>
            </div>

        </div>
        <div style="text-align: center; color: #666; font-size: 13px; padding: 2px 0;">
            <div id="OrderText">
            </div>
        </div>
    </div>
    <script>
        var kData = {};
        var kTask = new Array();

        var r = function (type, data, callback) {
            $.ajax({
                type: 'post',
                dataType: "json",
                url: '?do=' + type,
                data: data,
                success: function (result) {
                    callback(result)
                }
            });
        }
        $("#orderTypes").on('change', function () {
            var typeid = $(this).val();
            if (typeid == null || typeid == "") {
                $('#orderTotal').val('请选择业务');
                return;
            }
            var param = typeid.split('/')[1];
            typeid = typeid.split('/')[0];

            $('#orderUrls').val('');
            $('#orderNum').val('');
            $('.hInput').hide();
            $('.hInput').find('.w_input').val('');

            //console.log('check_param', param);
            switch (param) {
                case "customTimes":
                    $('#orderTime').closest('.ccvoYb').show();
                    break;
                case "comment_list":
                    $('#orderNote').closest('.ccvoYb').show();
                    break;
                case "custom-comment":
                    $('#orderComments').closest('.ccvoYb').show();
                    break;
                default:
                    break;
            }
            r('json_info', { typeid: typeid }, function (result) {
                //console.log("result", result);
                if (result.code == 1) {
                    $('#OrderText').text(result.tixin);
                    kData.appid = typeid;
                    kData.dianshu = parseFloat(result.dianshu);
                    $('#orderTotal').val('单价' + kData.dianshu + '点，累计消费' + kData.dianshu + '点');
                }
            })
        })

        //去除数组重复
        function unique(arr) {
            var result = [], hash = {};
            for (var i = 0, elem; (elem = arr[i]) != null; i++) {
                if (!hash[elem]) {
                    result.push(elem);
                    hash[elem] = true;
                }
            }
            return result;
        }

        $('#orderComments').on('keyup', function () {
            var comment_data = $(this).val();
            var arr = comment_data.split('\n');
            arr = unique(arr);
            //console.log('comment_data', arr);
            var new_comment_data = arr.join('\n');
            $('#orderNum').val(arr.length);
            check_total_dianshu();
            if (new_comment_data != comment_data) {
                $('#orderComments').val(new_comment_data);
            }
        });

        $("#orderUrls").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderNum").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderTime").on('keyup', function () {
            check_total_dianshu();
        })

        $("#orderTask_Btn").on('click', function () {
            if ($(this).hasClass("submit_status")) {
                return;
            }
            $(this).addClass("submit_status");
            check_total_dianshu();
            pushTask();
        })

        var check_total_dianshu = function () {
            kTask = [];
            var taskNum = $("#orderNum").val();
            var taskID = $("#orderUrls").val();
            if (taskID.indexOf("    ") != -1 || taskID.indexOf("----") != -1 || taskID.indexOf(",") != -1 || taskID.indexOf("，") != -1) {
                var arr = taskID.split('\n');
                taskNum = 0;
                for (var i = 0; i < arr.length; i++) {
                    var arr2;
                    if (arr[i].indexOf("----") != -1) {
                        arr2 = arr[i].split("----");
                    } else if (arr[i].indexOf(",") != -1) {
                        arr2 = arr[i].split(",");
                    } else if (arr[i].indexOf("，") != -1) {
                        arr2 = arr[i].split("，");
                    } else {
                        arr2 = arr[i].split("    ");
                    }
                    if (arr2.length == 2) {
                        taskNum += parseInt(arr2[1]);

                        kTask.push({
                            id: arr2[0],
                            num: parseInt(arr2[1])
                        })
                    }
                }
            } else {
                if (taskNum == "" || isNaN(taskNum) || parseInt(taskNum) < 0) {
                    taskNum = "0";
                }

                kTask.push({
                    id: taskID,
                    num: parseInt(taskNum)
                })
            }
            taskInfo = new Array();

            var total_fee = (kData.dianshu * parseInt(taskNum)).toFixed(4);

            var customTimes = $('#orderTime').val();
            if (customTimes != "") {
                total_fee = parseFloat(total_fee) * parseInt(customTimes);
            }

            $('#orderTotal').val('单价' + kData.dianshu + '点，累计消费' + parseFloat(total_fee) + '点');

            //console.log("kTask", JSON.stringify(kTask));
        }

        $("#orderTypes").trigger('change');

        var taskInfo = new Array();
        var _task;
        var pushTask = function () {
            _task = kTask.shift();
            if (typeof (_task) == "undefined") {
                var task_result = "";
                for (var i = 0; i < taskInfo.length; i++) {
                    task_result += '链接:' + taskInfo[i].task.id + ' 数量:' + taskInfo[i].task.num + ' 下单结果:' + taskInfo[i].result + '\n';
                }
                alert(task_result);
                $("#orderTask_Btn").removeClass("submit_status");

                $("#orderTypes").trigger('change');
                return;
            }
            var typeid = $("#orderTypes").val();
            var param = typeid.split('/')[1];
            typeid = typeid.split('/')[0];

            var task_param = "";
            switch (param) {
                case "customTimes":
                    task_param = $('#orderTime').val();
                    break;
                case "comment_list":
                    task_param = $('#orderNote').val();
                    break;
                case "custom-comment":
                    task_param = $('#orderComments').val();
                    break;
                default:
                    break;
            }

            $.ajax({
                type: 'post',
                url: '?do=orderTask',
                dataType: "json",
                data: { typeid: kData.appid, task_id: _task.id, task_num: _task.num, task_param: task_param },
                success: function (result) {
                    console.log(result, 'login', result.code);
                    taskInfo.push({ task: _task, result: (result.code == 1 ? '成功' : result.msg) });
                    pushTask();
                }, error: function (ex) {
                    taskInfo.push({ task: _task, result: '异常' });
                }
            });
        }
        var taskFinish = function () { }
    </script>

    <style>
        /* 表单样式增强 */
        .form-group {
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            padding-left: 4px;
        }

        .form-select, .form-textarea {
            border: 2px solid #e5e7eb;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-select:focus, .form-textarea:focus, .w_input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        .total-input {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
            color: #1f2937 !important;
            font-weight: 600;
            border: 2px solid #d1d5db;
        }

        /* 提交按钮增强 */
        .submit-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
            border-radius: 12px !important;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .submit_status {
            background: #9ca3af !important;
            color: #6b7280 !important;
            cursor: not-allowed !important;
            transform: none !important;
            box-shadow: none !important;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .submit_status:hover {
            background: #9ca3af !important;
            color: #6b7280 !important;
            box-shadow: none !important;
            transform: none !important;
        }

        .submit_status::before {
            display: none;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .classtype1 {
                padding: 20px 0;
            }

            .classtype1 a {
                min-width: 50px;
                padding: 12px 15px;
            }

            .submit-btn {
                width: 100% !important;
                margin: 0 20px;
            }
        }
    </style>
</asp:Content>

